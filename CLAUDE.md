# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Hajimi King is a GitHub code scanner that searches for potentially exposed API keys (specifically Google Gemini API keys) in public repositories. It uses GitHub's search API to find files matching specific patterns, then validates any discovered keys to check if they're active.

## Key Architecture Components

1. **Main Application** (`app/hajimi_king.py`): Core logic for searching, processing, and validating API keys
2. **Configuration** (`common/config.py`): Environment-based configuration management using python-dotenv
3. **GitHub Utilities** (`utils/github_utils.py`): Handles GitHub API interactions and search operations
4. **File Management** (`utils/file_manager.py`): Manages all file I/O operations, checkpointing, and result storage
5. **Query Management** (`queries.txt`): Defines search patterns for finding potential API keys

## Common Development Commands

### Install Dependencies
```bash
uv pip install -r pyproject.toml
```

### Run the Application
```bash
python app/hajimi_king.py
```

### Environment Setup
1. Copy the example environment file: `cp env.example .env`
2. Edit `.env` to add your GitHub tokens (required)
3. Adjust other configuration values as needed

### Docker Deployment
```bash
# Use the first_deploy.sh script for initial setup
chmod +x first_deploy.sh
./first_deploy.sh
```

### Docker Management
```bash
# View service status
docker-compose ps

# View real-time logs
docker-compose logs -f

# Stop service
docker-compose down

# Restart service
docker-compose up -d
```

## Key Architecture Patterns

1. **Checkpoint System**: Uses JSON-based checkpoints and SHA tracking to avoid reprocessing files
2. **Rate Limiting Handling**: Implements token rotation and exponential backoff for GitHub API
3. **Incremental Scanning**: Tracks last scan time and processed queries to resume operations
4. **Multiple Output Files**: Segregates results into valid keys, rate-limited keys, and detailed logs
5. **Dynamic File Naming**: Time-based filenames for organizing results by date/hour

## Data Flow

1. Load search queries from `queries.txt`
2. For each query, search GitHub API for matching files
3. Process each found file:
   - Skip if already scanned (SHA tracking)
   - Skip if in blacklisted paths (docs, examples, etc.)
   - Skip if repository is too old
   - Extract potential keys using regex patterns
   - Validate keys using Google Generative AI API
4. Save results to timestamped files in the data directory
5. Update checkpoint with processed items

## Configuration Files

- `.env`: Primary configuration (GitHub tokens, paths, filters)
- `queries.txt`: Search expressions for finding potential keys
- `data/checkpoint.json`: Tracks scan progress and processed queries
- `data/scanned_shas.txt`: Tracks already-processed file SHAs

## Result Files

Results are saved in the `data/` directory with timestamped filenames:
- `keys_valid_*.txt`: Confirmed valid API keys
- `keys_valid_detail_*.log`: Detailed logs of valid key findings
- `gemini_key_429_*.txt`: Keys that triggered rate limiting
- `gemini_key_429_detail_*.log`: Detailed logs of rate-limited keys